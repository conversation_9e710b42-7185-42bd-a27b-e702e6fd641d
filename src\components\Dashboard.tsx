import React, { useState, useEffect } from 'react';
import { Box, Snackbar } from '@mui/material';
import LeftPanelNav, { PanelType } from './LeftPanelNav';
import DataPage from './DataPage';
import ChartsPage from './ChartsPage';
import ErrorDisplay from './common/ErrorDisplay';
import useChartData from '../hooks/useChartData';
import { ApiError } from '../services/apiService';
import useAnalytics from '../hooks/useAnalytics';

// Utility function for smooth scroll to top of main content area
const scrollMainContentToTop = (smooth: boolean = true) => {
  // Find the main content area and scroll it to top
  const mainContentArea = document.querySelector('[data-main-content="true"]');
  if (mainContentArea) {
    mainContentArea.scrollTo({
      top: 0,
      left: 0,
      behavior: smooth ? 'smooth' : 'auto'
    });
  }
  // Also scroll window to top as fallback
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: smooth ? 'smooth' : 'auto'
  });
};

const Dashboard: React.FC<{ onNavigate: (page: string) => void }> = ({ }) => {
  const {
    charts,
    addChartData,
    updateChartData,
    resetChartData,
  } = useChartData();

  const [availableColumns, setAvailableColumns] = useState<string[]>([]);
  const [isClearing, setIsClearing] = useState(false);
  const [error, setError] = useState<ApiError | null>(null);
  const [loadingStep, setLoadingStep] = useState(0);
  const [showSteps, setShowSteps] = useState(false);
  const [currentPanel, setCurrentPanel] = useState<PanelType>('data');

  // Analytics hook
  const { trackNavigation, trackEvent } = useAnalytics();

  const clearError = () => setError(null);

  // Helper to animate loading steps
  const runLoadingSteps = async () => {
    setShowSteps(true);
    const LOADING_STEPS = [
      'Reading Data',
      'Identifying Data Points',
      'Aggregating Data',
      'Identifying Suitable Charts',
      'Preparing Charts'
    ];
    for (let i = 0; i < LOADING_STEPS.length; i++) {
      setLoadingStep(i);
      await new Promise(resolve => setTimeout(resolve, 600));
    }
  };

  // New handler for when upload starts - triggers progress steps immediately
  const handleUploadStart = async () => {
    setIsClearing(true);
    setLoadingStep(0);
    // Start loading steps animation immediately
    runLoadingSteps();
  };

  const handleDataProcessed = async (responseData: { charts: any[], available_columns: string[] }) => {
    // Progress steps are already running, just process the data
    resetChartData();

    try {
      if (responseData && Array.isArray(responseData.charts) && responseData.charts.length > 0) {
        const chartsWithIds = responseData.charts.map((chart, index) => ({
          ...chart,
          id: chart.id || `chart-${Date.now()}-${index}`,
        }));
        addChartData(chartsWithIds);
        setAvailableColumns(responseData.available_columns || []);

       
      // Track successful chart generation
        await trackEvent('CHART_GENERATION', {
          success: true,
          charts_generated: chartsWithIds.length,
          chart_types: chartsWithIds.map(chart => chart.chart_type),
          available_columns: responseData.available_columns?.length || 0,
          processing_completed: new Date().toISOString()
        });
     

        // Auto-switch to charts page after successful data processing
        const fromPanel = currentPanel;
        setCurrentPanel('charts');

        // Track navigation to charts page asynchronously (fire-and-forget)
        trackNavigation(fromPanel, 'charts', {
          charts_count: chartsWithIds.length,
          auto_navigation: true,
          reason: 'successful_upload'
        }).catch(error => {
          console.warn('Analytics tracking failed:', error);
        });
      } else {
        throw new Error("Invalid or empty chart data received");
      }
    } catch (err: any) {
      // Track processing error
      await trackEvent('ERROR_OCCURRED', {
        error_type: 'data_processing_failed',
        error_message: err.message || 'Unknown error',
        stage: 'post_upload_processing'
      });

      const apiError: ApiError = {
        message: err.message || 'An error occurred while processing data.',
        type: 'error'
      };
      setError(apiError);
    }

    setIsClearing(false);
    setShowSteps(false); // Hide steps after processing
  };

  const handleError = (error: ApiError) => {
    setError(error);
    setIsClearing(false);
    setShowSteps(false);
  };

  const handleChartUpdate = (id: string | number, updatedChartData: any) => {
    updateChartData(Number(id), updatedChartData);
  };

  const handlePanelChange = (panel: PanelType) => {
    const fromPanel = currentPanel;
    setCurrentPanel(panel);

    // Track navigation asynchronously (fire-and-forget)
    trackNavigation(fromPanel, panel, {
      charts_count: charts.length,
      has_data: charts.length > 0
    }).catch(error => {
      console.warn('Analytics tracking failed:', error);
    });
  };

  // Scroll to top when panel changes
  useEffect(() => {
    scrollMainContentToTop();
  }, [currentPanel]);

  const handleSwitchToDataPage = () => {
    const fromPanel = currentPanel;
    setCurrentPanel('data');

    // Track navigation back to data page asynchronously (fire-and-forget)
    trackNavigation(fromPanel, 'data', {
      charts_count: charts.length,
      reason: 'upload_new_data'
    }).catch(error => {
      console.warn('Analytics tracking failed:', error);
    });
  };

  // Render main dashboard
  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'row',
      width: '100%',
      minHeight: '100vh',
      backgroundColor: 'white',
    }}>
      {/* Left Panel Navigation */}
      <LeftPanelNav
        currentPanel={currentPanel}
        onPanelChange={handlePanelChange}
      />

      {/* Main Content Area */}
      <Box
        data-main-content="true"
        sx={{
          flexGrow: 1,
          backgroundColor: 'white',
          overflow: 'auto',
          minHeight: '100vh',
          marginLeft: { xs: '60px', sm: '60px', md: '200px' }, // Account for fixed left panel
        }}
      >
        {currentPanel === 'data' ? (
          <DataPage
            onDataProcessed={handleDataProcessed}
            onError={handleError}
            onUploadStart={handleUploadStart}
            isLoading={isClearing}
            loadingStep={loadingStep}
            showSteps={showSteps}
          />
        ) : (
          <ChartsPage
            charts={charts}
            availableColumns={availableColumns}
            onChartUpdate={handleChartUpdate}
            onSwitchToDataPage={handleSwitchToDataPage}
          />
        )}
      </Box>

      {/* Snackbar for errors */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={clearError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{ mt: 2 }}
      >
        <ErrorDisplay
          error={error || 'An error occurred'}
          variant="snackbar"
        />
      </Snackbar>
    </Box>
  );
};

export default Dashboard;
