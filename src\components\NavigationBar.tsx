import React from "react";
import {
  AppBar,
  Box,
  Toolbar,
  Typography,
} from "@mui/material";
import ProfessionalButton from "./common/ProfessionalButton";
import logo from "../assets/dchartify_logo.png";
import { useAnalytics } from "../hooks/useAnalytics";
import { colors } from "../theme";

interface NavigationBarProps {
  onNavigate: (page: string) => void;
  isSignedIn: boolean;
  currentPage: string;
}

const NavigationBar: React.FC<NavigationBarProps> = ({ onNavigate, isSignedIn, currentPage }) => {
  const { trackNavigation } = useAnalytics();

  const handleNavigate = (page: string) => {
    // Navigate immediately for better UX
    onNavigate(page);

    // Track page-level navigation asynchronously (fire-and-forget)
    trackNavigation(currentPage, page, {
      is_signed_in: isSignedIn,
      navigation_source: 'navigation_bar'
    }).catch(error => {
      // Silently handle analytics errors to not affect user experience
      console.warn('Analytics tracking failed:', error);
    });
  };
  return (
    <AppBar position="fixed" sx={{ backgroundColor: colors.background, boxShadow: '0 1px 3px rgba(46, 46, 46, 0.1)', zIndex: 1300 }}>
      <Toolbar sx={{ justifyContent: "space-between", py: 2 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 1.5,
            padding: "8px 16px",
            borderRadius: "12px",
            backgroundColor: colors.background,
            border: "1px solid rgba(46, 46, 46, 0.08)",
            boxShadow: "0 2px 8px rgba(46, 46, 46, 0.06)",
            transition: "all 0.2s ease-in-out",
            "&:hover": {
              boxShadow: "0 4px 12px rgba(46, 46, 46, 0.1)",
              transform: "translateY(-1px)",
            },
          }}
        >
          <img
            src={logo}
            alt="DChartify Logo"
            style={{ height: "32px", width: "32px", borderRadius: "4px" }}
          />

         <Box sx={{
           display: "flex",
           flexDirection: "column",
           alignItems: "flex-start",
           position: "relative",
           justifyContent: "center", // Center vertically with logo
           height: "32px" // Match logo height for proper alignment
         }}>
          <Typography
            variant="h1"
            sx={{
              fontSize: { xs: "1.25rem", md: "1.5rem" },
              fontWeight: 700,
              color: colors.textPrimary,
              textAlign: "left",
              fontFamily: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
              letterSpacing: "-0.02em",
              lineHeight: 1,
              margin: 0, // Remove default margins
              padding: 0, // Remove default padding
            }}
          >
            <Box component="span" sx={{
              position: "relative",
              display: "inline-flex",
              alignItems: "baseline", // Align text baseline
              height: "fit-content"
            }}>
              DChartify
              <Typography
                variant="caption"
                sx={{
                  color: colors.textSecondary,
                  fontSize: "0.6rem",
                  lineHeight: 1,
                  ml: 0.4,
                  transform: "translateY(6px)", // Move beta text further down
                  position: "relative",
                }}
              >
                beta
              </Typography>
            </Box>
          </Typography>
        </Box>
        </Box>

        <Box sx={{ display: "flex", gap: 1.5 }}>
          <ProfessionalButton
            variant="text"
            onClick={() => handleNavigate("home")}
            sx={{
              fontWeight: currentPage === "home" ? 600 : 500,
              color: currentPage === "home" ? colors.accentPrimary : colors.textSecondary,
              fontFamily: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
              fontSize: "0.95rem",
              px: 2,
              py: 1,
              borderRadius: "8px",
              transition: "all 0.2s ease-in-out",
              "&:hover": {
                backgroundColor: `rgba(59, 130, 246, 0.08)`,
                color: colors.accentPrimary,
              },
            }}
          >
            Home
          </ProfessionalButton>



          {!isSignedIn && (
            <ProfessionalButton
              variant="outlined"
              onClick={() => handleNavigate("signin")}
              sx={{
                fontWeight: currentPage === "signin" ? 600 : 500,
                fontFamily: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                fontSize: "0.95rem",
                px: 2.5,
                py: 1,
                borderRadius: "8px",
                borderColor: "rgba(122, 122, 122, 0.3)",
                color: colors.textSecondary,
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  borderColor: colors.accentPrimary,
                  color: colors.accentPrimary,
                  backgroundColor: `rgba(59, 130, 246, 0.04)`,
                },
              }}
            >
              Sign In
            </ProfessionalButton>
          )}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default NavigationBar;
