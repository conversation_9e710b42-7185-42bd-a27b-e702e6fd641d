const config = {
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/v1',
  googleClientId: import.meta.env.VITE_GOOGLE_CLIENT_ID || 'YOUR_DEFAULT_CLIENT_ID',
  apiTimeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000', 10), // Default 30 seconds
  showTechnicalDetails: import.meta.env.VITE_SHOW_TECHNICAL_DETAILS === 'true' || import.meta.env.MODE === 'development', // Show technical details in development or when explicitly enabled
};

export default config;
