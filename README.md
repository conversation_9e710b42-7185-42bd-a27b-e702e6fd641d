# InfoCharts Project Summary
InfoCharts is an interactive dashboard application built with React 19, TypeScript, and Vite that allows users to upload CSV data and visualize it through various chart types using Plotly.js.
The application follows a client-server architecture with a backend API running at `http://localhost:8000` that handles data processing and analytics tracking.

## Features

- 📊 Interactive data visualization with Plotly.js
- 📁 CSV/Excel file upload and parsing
- 🎨 Dark mode UI with Material-UI (MUI)
- 📱 Responsive layout
- ⚡ Real-time chart updates
- 🛠️ Customizable chart properties
- 📊 Multiple Advanced chart types support (heatmap, histogram, bar, pie, scatter, line, box plots, etc.)
- Google OAuth integration
- Comprehensive analytics implementation
-Chart grouping functionality
- Analytics tracking for user behavior insights

## Tech Stack

- React 19
- TypeScript
- Vite 6
- Material-UI (MUI) 7
- Plotly.js
- React Grid Layout
- Emotion (for styled components)

## Prerequisites

- Node.js >= 18.18.0
- npm or yarn package manager

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd interactive-dashboard
```

2. Install dependencies:
```bash
npm install
```

## Development

Start the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Project Structure

```
src/
├── components/
│   ├── Chart.tsx           # Chart component with Plotly integration
│   ├── ChartGrid.tsx       # Grid layout for multiple charts
│   ├── Dashboard.tsx       # Main dashboard layout
│   ├── FileUpload.tsx      # CSV file upload component
│   ├── PropertiesPanel.tsx # Chart properties editor
│   └── Sidebar.tsx         # Navigation sidebar
├── services/
│   └── csvParser.ts        # CSV parsing utilities
├── theme.ts                # MUI theme configuration
├── App.tsx                 # Root application component
└── main.tsx               # Application entry point
```

## How It Works

1. **File Upload**: Users can upload CSV files through the FileUpload component in the left sidebar.

2. **Data Processing**: The uploaded CSV is parsed using the csvParser service, which extracts data series and determines appropriate chart types.

3. **Visualization**: Charts are rendered using Plotly.js through the Chart component, which supports various chart types and configurations.

4. **Customization**: Users can customize charts through the PropertiesPanel, including:
   - Chart titles
   - Axis labels
   - Line widths
   - Other visual properties

5. **Layout**: Charts are organized in a responsive grid layout that adapts to different screen sizes.

## Backend Integration

The application expects a backend service running at `http://localhost:8000` with the following endpoints:

- `POST /v1/upload-csv`: Processes uploaded CSV files
- `POST /v1/update-chart`: Handles chart updates and transformations

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

[Add your license information here]
