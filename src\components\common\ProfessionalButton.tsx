import React from 'react';
import { Button, ButtonProps, SxProps, Theme } from '@mui/material';

interface ProfessionalButtonProps extends Omit<ButtonProps, 'variant'> {
  variant?: 'primary' | 'secondary' | 'outlined' | 'text' | 'contained';
  size?: 'small' | 'medium' | 'large';
}

const ProfessionalButton: React.FC<ProfessionalButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  children,
  sx,
  ...props
}) => {
  const getVariantStyles = () => {
    const baseStyles: SxProps<Theme> = {
      borderRadius: 2,
      textTransform: 'none',
      fontWeight: 500,
      transition: 'all 0.3s ease',
    };

    switch (variant) {
      case 'primary':
        return {
          variant: 'contained' as const,
          color: undefined,
          baseStyles: {
            ...baseStyles,
            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
            '&:hover': {
              boxShadow: '0 8px 16px rgba(0,0,0,0.2)',
              transform: 'translateY(-2px)',
            },
          },
        };
      case 'secondary':
        return {
          variant: 'contained' as const,
          color: 'secondary' as const,
          baseStyles: {
            ...baseStyles,
            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
            '&:hover': {
              boxShadow: '0 8px 16px rgba(0,0,0,0.2)',
              transform: 'translateY(-2px)',
            },
          },
        };
      case 'outlined':
        return {
          variant: 'outlined' as const,
          color: undefined,
          baseStyles: {
            ...baseStyles,
            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
            '&:hover': {
              boxShadow: '0 8px 16px rgba(0,0,0,0.2)',
              transform: 'translateY(-2px)',
            },
          },
        };
      case 'text':
        return {
          variant: 'text' as const,
          color: undefined,
          baseStyles: {
            ...baseStyles,
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
              transform: 'translateY(-1px)',
            },
          },
        };
      case 'contained':
        return {
          variant: 'contained' as const,
          color: undefined,
          baseStyles: {
            ...baseStyles,
            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
            '&:hover': {
              boxShadow: '0 8px 16px rgba(0,0,0,0.2)',
              transform: 'translateY(-2px)',
            },
          },
        };
      default:
        return {
          variant: 'contained' as const,
          color: undefined,
          baseStyles: {
            ...baseStyles,
            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
            '&:hover': {
              boxShadow: '0 8px 16px rgba(0,0,0,0.2)',
              transform: 'translateY(-2px)',
            },
          },
        };
    }
  };

  const variantStyles = getVariantStyles();

  // Properly merge sx styles
  const mergedSx: SxProps<Theme> = [
    variantStyles.baseStyles,
    ...(Array.isArray(sx) ? sx : [sx])
  ];

  return (
    <Button
      variant={variantStyles.variant}
      color={variantStyles.color}
      size={size}
      sx={mergedSx}
      {...props}
    >
      {children}
    </Button>
  );
};

export default ProfessionalButton;
