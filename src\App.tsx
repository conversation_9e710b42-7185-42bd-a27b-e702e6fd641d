import React, { useState, useEffect } from "react";
import { ThemeProvider, CssBaseline, Box } from "@mui/material";
import Dashboard from "./components/Dashboard";
import Home from "./components/Home";
import SignIn from "./components/SignIn";
import Pricing from "./components/Pricing";

import NavigationBar from "./components/NavigationBar";
import ErrorBoundary from "./components/common/ErrorBoundary";
import VersionChecker from "./components/common/VersionChecker";
import { ErrorModalProvider } from "./contexts/ErrorModalContext";
import { useGlobalErrorHandler } from "./hooks/useGlobalErrorHandler";
import { theme } from "./theme";
import { tryRefreshToken, setAuthFailureCallback, clearAuthToken } from "./services/apiService";

// Utility function for smooth scroll to top
const scrollToTop = (smooth: boolean = true) => {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: smooth ? 'smooth' : 'auto'
  });
};


// Helper to check if user is signed in and token is valid/refreshable
function isUserSignedIn() {
  const accessToken = localStorage.getItem("access_token");
  if (!accessToken) return false;
  // Optionally, check expiry here if you store expiry in localStorage
  return true;
}

const App: React.FC = () => {
  const [currentPage, setCurrentPage] = useState("home");
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Set up global error handling
  useGlobalErrorHandler({
    onError: (error, context) => {
      console.error(`Global error caught in ${context}:`, error);
      // Could integrate with external error reporting service here
    },
    enableConsoleLogging: true,
    enableErrorReporting: false // Set to true when integrating with external service
  });

  // Handle authentication failure - redirect to sign-in
  const handleAuthFailure = React.useCallback(() => {
    console.log('Authentication failed, redirecting to sign-in...');
    clearAuthToken(); // Clear invalid tokens
    setCurrentPage("signin");
  }, []);

  React.useEffect(() => {
    // Set up the auth failure callback
    setAuthFailureCallback(handleAuthFailure);

    // On mount, check if access_token exists and is valid, else try refresh
    const checkAuth = async () => {
      const accessToken = localStorage.getItem("access_token");
      if (accessToken) {
        // Optionally, check expiry here if you store expiry in localStorage
        setCurrentPage("dashboard");
        setIsCheckingAuth(false);
        return;
      }
      // Try refresh if refresh_token exists
      const refreshToken = localStorage.getItem("refresh_token");
      if (refreshToken) {
        const refreshResult = await tryRefreshToken();
        if (refreshResult.success) {
          setCurrentPage("dashboard");
        } else {
          setCurrentPage("signin");
        }
      } else {
        setCurrentPage("home");
      }
      setIsCheckingAuth(false);
    };
    checkAuth();
  }, [handleAuthFailure]);

  // Scroll to top when page changes
  useEffect(() => {
    if (!isCheckingAuth) {
      scrollToTop();
    }
  }, [currentPage, isCheckingAuth]);

  const handleStart = async () => {
    // If already signed in, go to dashboard
    if (isUserSignedIn()) {
      setCurrentPage("dashboard");
      return;
    }
    // Try refresh if refresh_token exists
    const refreshToken = localStorage.getItem("refresh_token");
    if (refreshToken) {
      const refreshResult = await tryRefreshToken();
      if (refreshResult.success) {
        setCurrentPage("dashboard");
        return;
      }
    }
    setCurrentPage("signin");
  };

  const handleSignInSuccess = () => {
    setCurrentPage("dashboard");
  };

  const renderPage = () => {
    if (isCheckingAuth) return null; // Or a loader
    switch (currentPage) {
      case "dashboard":
        return <Dashboard onNavigate={setCurrentPage} />;
      case "pricing":
        return <Pricing />;
      case "signin":
        return <SignIn onSignInSuccess={handleSignInSuccess} />;

      default:
        return <Home onStart={handleStart} />;
    }
  };

  return (
    <ErrorBoundary showDetails={true}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <ErrorModalProvider onNavigate={setCurrentPage}>
          <Box
            sx={{
              minHeight: "100vh",
              backgroundColor: "#ffffff",
            }}
          >
            <NavigationBar
              onNavigate={setCurrentPage}
              isSignedIn={isUserSignedIn()}
              currentPage={currentPage}
            />
            <Box sx={{ paddingTop: currentPage === "dashboard" ? '80px' : '0px' }}>
              {renderPage()}
            </Box>

            {/* Global Version Checker */}
            <VersionChecker
              checkInterval={3 * 60 * 1000} // Check every 3 minutes
              showNotification={true}
              autoRefresh={false} // Let user choose when to refresh
            />
          </Box>
        </ErrorModalProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App;
