import React, { useEffect, useState } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Link,
  Snackbar,
  <PERSON><PERSON>,
  <PERSON><PERSON>
} from "@mui/material";
import StartIcon from "@mui/icons-material/Launch";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import BarChartIcon from "@mui/icons-material/BarChart";
import PreviewIcon from "@mui/icons-material/Preview";
import DownloadIcon from "@mui/icons-material/Download";
import EditIcon from "@mui/icons-material/Edit";
import InsightsIcon from "@mui/icons-material/Insights";
import PresentationIcon from "@mui/icons-material/Slideshow";
import ArticleIcon from "@mui/icons-material/Article";
import ShareIcon from "@mui/icons-material/Share";
import DescriptionIcon from "@mui/icons-material/Description";
import BusinessIcon from "@mui/icons-material/Business";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ProfessionalButton from "./common/ProfessionalButton";
import { motion } from "framer-motion";
import { useVersionCheck } from "../hooks/useVersionCheck";

// Utility function for smooth scroll to top
const scrollToTop = (smooth: boolean = true) => {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: smooth ? 'smooth' : 'auto'
  });
};

import chart1 from "../assets/chart1.png";
import chart2 from "../assets/chart2.png";
import chart3 from "../assets/chart3.png";
import chart4 from "../assets/chart4.png";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: (i = 0) => ({
    opacity: 1,
    y: 0,
    transition: { delay: i * 0.2, duration: 0.6, ease: "easeOut" },
  }),
};

const Home: React.FC<{ onStart: () => void }> = ({ onStart }) => {
  // Dynamic hero titles
  const heroTitles = [
    "Turn Boring Spreadsheets into\nStunning Visual Stories in Seconds",
    "Stop Wasting Hours on Excel Charts \nCreate Beautiful Visuals Instantly",
    "Your Data Deserves Better.\nTransform It Into Insightful, Share-Worthy Charts",
    "Ditch the Rows & Columns.\nStart Telling Visual Stories That Win Decisions",
    "Impress Clients, Win Meetings, and\nDeliver Insights They'll Remember",
    "No More Cluttered Excel Sheets. \nSee What Really Matters with Clean, Smart Charts",
    "One Click. Beautiful Charts.\nEndless Impact.",
    "Make Your Data Speak Louder with\nEye-Catching Visualizations",
    "From Raw Data to\nBoardroom-Ready Charts in Minutes",
    "If a Picture's Worth 1000 Words \nImagine What Your Charts Could Say"
  ];

  // Get random title on component mount
  const [currentTitle, setCurrentTitle] = useState("");

  // Version check for automatic refresh
  const { hasUpdate, refreshApp } = useVersionCheck({
    checkInterval: 3 * 60 * 1000, // Check every 3 minutes
    forceRefreshOnUpdate: false, // Don't auto-refresh, let user choose
    showUpdateNotification: true,
    enableAutoRefresh: true
  });

  // State for update notification
  const [showUpdateSnackbar, setShowUpdateSnackbar] = useState(false);

  // Scroll to top when component mounts
  useEffect(() => {
    scrollToTop();
    // Set random title
    const randomIndex = Math.floor(Math.random() * heroTitles.length);
    setCurrentTitle(heroTitles[randomIndex]);
  }, []);

  // Show update notification when update is available
  useEffect(() => {
    if (hasUpdate) {
      setShowUpdateSnackbar(true);
    }
  }, [hasUpdate]);

  // Handle update refresh
  const handleRefreshApp = () => {
    setShowUpdateSnackbar(false);
    refreshApp();
  };

  return (
    <Box
      sx={{
        minHeight: "100vh", // Ensure at least viewport height, allow scrolling
        bgcolor: "#FFFFFF",
        display: "flex",
        flexDirection: "column",
        paddingTop: "80px", // Fix header overlap issue
      }}
    >
      {/* Hero Section */}
      <Box
        component="section"
        sx={{
          flexGrow: 0,
          py: { xs: 4, sm: 6, md: 8 },
          px: { xs: 2, sm: 3 },
          textAlign: "center",
          bgcolor: "#FFFFFF",
          display: "flex",
          alignItems: "center",
          boxSizing: "border-box",
        }}
      >
        <Container maxWidth="xl">
          <motion.div variants={fadeInUp} initial="hidden" animate="visible" custom={0}>
            <Typography
              variant="h2"
              component="h1"
              sx={{
                fontSize: { xs: "1.2rem", sm: "1.8rem", md: "2.5rem", lg: "3rem" },
                fontWeight: 800,
                mb: { xs: 2, sm: 3 },
                lineHeight: { xs: 1.4, sm: 1.3, md: 1.25, lg: 1.2 },
                whiteSpace: "pre-line", // Allow line breaks from \n
                px: { xs: 1, sm: 2 },
                color: "#1a1a1a",
                letterSpacing: "-0.02em",
                minHeight: "fit-content", // Ensure full height is available
                display: "block", // Ensure proper block display
                textAlign: "center",
              }}
            >
              {currentTitle}
            </Typography>
          </motion.div>

          <motion.div variants={fadeInUp} initial="hidden" animate="visible" custom={1}>
            <Typography
              variant="h5"
              sx={{
                mb: { xs: 3, sm: 4 },
                fontSize: { xs: "1rem", sm: "1.1rem", md: "1.2rem" },
                px: { xs: 1, sm: 2 },
                lineHeight: 1.5,
                color: "text.secondary",
                fontWeight: 400,
                opacity: 0.8,
              }}
            >
              With Your Data, Beautifully Chartified
            </Typography>
          </motion.div>

          <Box
            sx={{
              display: "flex",
              flexWrap: { xs: "wrap", md: "nowrap" },
              justifyContent: "center",
              alignItems: "center",
              gap: { xs: 1.5, sm: 2, md: 3 },
              mt: { xs: 1, sm: 1.5 },
              mb: { xs: 1.5 },
              py: { xs: 1.5, md: 2 },
              px: { xs: 1, sm: 2 },
              perspective: "1000px",
              overflowX: { xs: "auto", md: "visible" },
              overflowY: "hidden", // Prevent scrollbar during image animation
              minHeight: "150px", // Reserve space for images
              maxWidth: "100%",
            }}
          >
            {[chart1, chart2, chart3, chart4].map((img, index) => (
              <motion.img
                key={index}
                src={img}
                alt={`chart ${index + 1}`}
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
                custom={index * 0.2}
                whileHover={{ scale: 1.05, rotate: 0 }}
                style={{
                  width: "200px",
                  height: "150px",
                  objectFit: "contain",
                  borderRadius: 8,
                  boxShadow: "0 12px 24px rgba(0,0,0,0.15)",
                  transition: "all 0.3s ease",
                }}
              />
            ))}
          </Box>

          <motion.div variants={fadeInUp} initial="hidden" animate="visible" custom={2}>
            <Box sx={{ mt: { xs: 3, sm: 4, md: 5 }, mb: { xs: 2, sm: 3 } }}>
              <ProfessionalButton
                variant="outlined"
                startIcon={<StartIcon />}
                size="large"
                onClick={onStart}
                sx={{
                  fontSize: { xs: "1rem", sm: "1.1rem" },
                  px: { xs: 3, sm: 4 },
                  py: { xs: 1, sm: 1.5 },
                }}
              >
                Get Started Free
              </ProfessionalButton>
            </Box>
          </motion.div>

          <motion.div variants={fadeInUp} initial="hidden" animate="visible" custom={3}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                mt: { xs: 2, sm: 3 },
                fontSize: { xs: "0.85rem", sm: "0.95rem" },
                fontStyle: "italic",
                px: { xs: 2, sm: 3 },
                lineHeight: 1.5,
                maxWidth: { xs: "100%", sm: "80%", md: "70%" },
                mx: "auto",
              }}
            >
              Your Data is not stored anywhere, not used for any training, only for preparing chart.
            </Typography>
          </motion.div>
        </Container>
      </Box>

      {/* How to Use Section */}
      <Box
        component="section"
        sx={{
          py: { xs: 6, md: 8 },
          bgcolor: "#F8FAFC",
        }}
      >
        <Container maxWidth="lg">
          <motion.div variants={fadeInUp} initial="hidden" animate="visible" custom={4}>
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontSize: { xs: "1.8rem", md: "2.5rem" },
                fontWeight: 700,
                textAlign: "center",
                mb: { xs: 3, md: 5 },
                color: "#1a1a1a",
              }}
            >
              How to Use DChartify
            </Typography>
          </motion.div>

          <Box sx={{ display: "grid", gridTemplateColumns: { xs: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }, gap: 3 }}>
            {[
              {
                icon: <UploadFileIcon sx={{ fontSize: 40, color: "#3B82F6" }} />,
                title: "Upload Excel or CSV",
                description: "Simply drag and drop your data file or click to browse. We support Excel (.xlsx, .xls) and CSV formats.",
                step: "01"
              },
              {
                icon: <BarChartIcon sx={{ fontSize: 40, color: "#10B981" }} />,
                title: "View Charts",
                description: "Instantly see beautiful, professional charts generated from your data with smart formatting and styling.",
                step: "02"
              },
              {
                icon: <PreviewIcon sx={{ fontSize: 40, color: "#8B5CF6" }} />,
                title: "Preview Charts",
                description: "Click the preview icon to see your charts in full size with all details clearly visible.",
                step: "03"
              },
              {
                icon: <EditIcon sx={{ fontSize: 40, color: "#EF4444" }} />,
                title: "Modify Charts",
                description: "Customize chart titles, axis labels, and styling to match your presentation needs.",
                step: "04"
              },
              {
                icon: <InsightsIcon sx={{ fontSize: 40, color: "#9C27B0" }} />,
                title: "Get AI Insights",
                description: "Click the insights icon to get AI-powered analysis with executive summaries, data insights, and recommendations.",
                step: "05"
              },
              {
                icon: <DownloadIcon sx={{ fontSize: 40, color: "#F59E0B" }} />,
                title: "Copy or Download",
                description: "Copy charts to clipboard for presentations or download as high-quality PNG images.",
                step: "06"
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
                custom={5 + index}
              >
                <Paper
                  elevation={2}
                  sx={{
                    p: 3,
                    height: "100%",
                    borderRadius: 3,
                    position: "relative",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      transform: "translateY(-4px)",
                      boxShadow: "0 12px 24px rgba(0,0,0,0.15)",
                    },
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      position: "absolute",
                      top: 16,
                      right: 16,
                      fontSize: "0.75rem",
                      fontWeight: 600,
                      color: "#9CA3AF",
                      backgroundColor: "#F3F4F6",
                      px: 1.5,
                      py: 0.5,
                      borderRadius: 2,
                    }}
                  >
                    {item.step}
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    {item.icon}
                  </Box>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      mb: 1.5,
                      color: "#1a1a1a",
                    }}
                  >
                    {item.title}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#6B7280",
                      lineHeight: 1.6,
                    }}
                  >
                    {item.description}
                  </Typography>
                </Paper>
              </motion.div>
            ))}
          </Box>
        </Container>
      </Box>

      {/* Use Cases Section */}
      <Box
        component="section"
        sx={{
          py: { xs: 6, md: 8 },
          bgcolor: "#FFFFFF",
        }}
      >
        <Container maxWidth="lg">
          <motion.div variants={fadeInUp} initial="hidden" animate="visible" custom={11}>
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontSize: { xs: "1.8rem", md: "2.5rem" },
                fontWeight: 700,
                textAlign: "center",
                mb: { xs: 3, md: 5 },
                color: "#1a1a1a",
              }}
            >
              Perfect for Every Use Case
            </Typography>
          </motion.div>

          <Box sx={{ display: "grid", gridTemplateColumns: { xs: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }, gap: 3 }}>
            {[
              {
                icon: <PresentationIcon sx={{ fontSize: 40, color: "#3B82F6" }} />,
                title: "Presentations",
                description: "Create stunning slides with professional charts that captivate your audience and clearly communicate your data story.",
                step: "01"
              },
              {
                icon: <ArticleIcon sx={{ fontSize: 40, color: "#10B981" }} />,
                title: "Blog Posts",
                description: "Enhance your articles with compelling visual data that supports your narrative and engages readers.",
                step: "02"
              },
              {
                icon: <ShareIcon sx={{ fontSize: 40, color: "#8B5CF6" }} />,
                title: "Social Media",
                description: "Share impactful data visualizations that drive engagement and establish your thought leadership.",
                step: "03"
              },
              {
                icon: <DescriptionIcon sx={{ fontSize: 40, color: "#F59E0B" }} />,
                title: "Documentation",
                description: "Support technical documentation with clear, informative charts that explain complex data relationships.",
                step: "04"
              },
              {
                icon: <BusinessIcon sx={{ fontSize: 40, color: "#EF4444" }} />,
                title: "Executive Reports",
                description: "Deliver executive-level insights with polished charts and AI-generated summaries for strategic decision-making.",
                step: "05"
              }
            ].map((useCase, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
                custom={12 + index}
              >
                <Paper
                  elevation={2}
                  sx={{
                    p: 3,
                    height: "100%",
                    borderRadius: 3,
                    position: "relative",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      transform: "translateY(-4px)",
                      boxShadow: "0 12px 24px rgba(0,0,0,0.15)",
                    },
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      position: "absolute",
                      top: 16,
                      right: 16,
                      fontSize: "0.75rem",
                      fontWeight: 600,
                      color: "#9CA3AF",
                      backgroundColor: "#F3F4F6",
                      px: 1.5,
                      py: 0.5,
                      borderRadius: 2,
                    }}
                  >
                    {useCase.step}
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    {useCase.icon}
                  </Box>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      mb: 1.5,
                      color: "#1a1a1a",
                    }}
                  >
                    {useCase.title}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#6B7280",
                      lineHeight: 1.6,
                    }}
                  >
                    {useCase.description}
                  </Typography>
                </Paper>
              </motion.div>
            ))}
          </Box>
        </Container>
      </Box>

      {/* FAQ Section */}
      <Box
        component="section"
        sx={{
          py: { xs: 6, md: 8 },
          bgcolor: "#F8FAFC",
        }}
      >
        <Container maxWidth="md">
          <motion.div variants={fadeInUp} initial="hidden" animate="visible" custom={17}>
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontSize: { xs: "1.8rem", md: "2.5rem" },
                fontWeight: 700,
                textAlign: "center",
                mb: { xs: 3, md: 5 },
                color: "#1a1a1a",
              }}
            >
              Frequently Asked Questions
            </Typography>
          </motion.div>

          <Box sx={{ mt: 4 }}>
            {[
              {
                question: "What file formats do you support?",
                answer: "We support Excel files (.xlsx, .xls) and CSV files. Simply upload your data and we'll automatically detect the format and generate appropriate charts."
              },
              {
                question: "Is my data secure and private?",
                answer: "Absolutely! Your data is not stored anywhere on our servers, not used for training, and only processed temporarily to generate your charts. We prioritize your data privacy and security."
              },
              {
                question: "Can I customize the charts after they're generated?",
                answer: "Yes! You can modify chart titles, axis labels, and styling to match your presentation needs. Use the modify icon on any chart to make adjustments."
              },
              {
                question: "What types of charts can I create?",
                answer: "DChartify automatically generates the most appropriate chart types based on your data, including bar charts, line charts, pie charts, heatmaps, area charts, and more."
              },
              {
                question: "How do I get AI-powered insights?",
                answer: "Click the insights icon on any chart to get AI-generated analysis including executive summaries, data insights, hidden patterns, and actionable recommendations."
              },
              {
                question: "Can I download or copy the charts?",
                answer: "Yes! You can copy charts directly to your clipboard for presentations or download them as high-quality PNG images for use in documents and reports."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
                custom={18 + index}
              >
                <Accordion
                  elevation={0}
                  sx={{
                    mb: 2,
                    borderRadius: 2,
                    "&:before": { display: "none" },
                    border: "1px solid rgba(0,0,0,0.08)",
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    sx={{
                      py: 2,
                      "&:hover": {
                        backgroundColor: "rgba(59, 130, 246, 0.04)",
                      },
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        color: "#1a1a1a",
                        fontSize: "1.1rem",
                      }}
                    >
                      {faq.question}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ pt: 0, pb: 3 }}>
                    <Typography
                      variant="body1"
                      sx={{
                        color: "#6B7280",
                        lineHeight: 1.6,
                      }}
                    >
                      {faq.answer}
                    </Typography>
                  </AccordionDetails>
                </Accordion>
              </motion.div>
            ))}
          </Box>
        </Container>
      </Box>

      {/* Footer Section */}
      <Box
        component="footer"
        sx={{
          py: { xs: 4, md: 6 },
          bgcolor: "#1F2937",
          color: "#FFFFFF",
        }}
      >
        <Container maxWidth="lg">
          <motion.div variants={fadeInUp} initial="hidden" animate="visible" custom={24}>
            <Box sx={{ display: "grid", gridTemplateColumns: { xs: "1fr", md: "2fr 1fr 1fr" }, gap: 4 }}>
              <Box>
                <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 700,
                      color: "#FFFFFF",
                      fontSize: "1.5rem",
                    }}
                  >
                    DChartify
                  </Typography>
                </Box>
                <Typography
                  variant="body1"
                  sx={{
                    color: "#D1D5DB",
                    lineHeight: 1.6,
                    mb: 3,
                  }}
                >
                  Transform your data into beautiful, professional charts with AI-powered insights.
                  Perfect for presentations, reports, and data storytelling.
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: "#9CA3AF",
                    fontSize: "0.85rem",
                  }}
                >
                  Your data privacy is our priority. No data storage, no training usage.
                </Typography>
              </Box>

              <Box>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    mb: 2,
                    color: "#FFFFFF",
                  }}
                >
                  Features
                </Typography>
                <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                  {["Excel & CSV Upload", "Instant Chart Generation", "AI-Powered Insights", "Copy & Download", "Chart Customization"].map((feature, index) => (
                    <Link
                      key={index}
                      href="#"
                      sx={{
                        color: "#D1D5DB",
                        textDecoration: "none",
                        fontSize: "0.9rem",
                        "&:hover": {
                          color: "#3B82F6",
                        },
                      }}
                    >
                      {feature}
                    </Link>
                  ))}
                </Box>
              </Box>

              <Box>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    mb: 2,
                    color: "#FFFFFF",
                  }}
                >
                  Use Cases
                </Typography>
                <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                  {["Presentations", "Blog Posts", "Social Media", "Documentation", "Executive Reports"].map((useCase, index) => (
                    <Link
                      key={index}
                      href="#"
                      sx={{
                        color: "#D1D5DB",
                        textDecoration: "none",
                        fontSize: "0.9rem",
                        "&:hover": {
                          color: "#3B82F6",
                        },
                      }}
                    >
                      {useCase}
                    </Link>
                  ))}
                </Box>
              </Box>
            </Box>

            <Divider sx={{ my: 4, borderColor: "#374151" }} />

            <Box
              sx={{
                display: "flex",
                flexDirection: { xs: "column", sm: "row" },
                justifyContent: "space-between",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: "#9CA3AF",
                  fontSize: "0.85rem",
                }}
              >
                ©2025 DChartify. All rights reserved.
              </Typography>

              <Box sx={{ display: "flex", gap: 3 }}>
                <Link
                  href="#"
                  sx={{
                    color: "#D1D5DB",
                    textDecoration: "none",
                    fontSize: "0.85rem",
                    "&:hover": {
                      color: "#3B82F6",
                    },
                  }}
                >
                  Privacy Policy
                </Link>
                <Link
                  href="#"
                  sx={{
                    color: "#D1D5DB",
                    textDecoration: "none",
                    fontSize: "0.85rem",
                    "&:hover": {
                      color: "#3B82F6",
                    },
                  }}
                >
                  Terms of Service
                </Link>
              </Box>
            </Box>
          </motion.div>
        </Container>
      </Box>

      {/* Update Notification Snackbar */}
      <Snackbar
        open={showUpdateSnackbar}
        onClose={() => setShowUpdateSnackbar(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        sx={{ mt: 8 }} // Account for fixed header
      >
        <Alert
          onClose={() => setShowUpdateSnackbar(false)}
          severity="info"
          variant="filled"
          action={
            <Button
              color="inherit"
              size="small"
              onClick={handleRefreshApp}
              sx={{
                fontWeight: 600,
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)'
                }
              }}
            >
              Refresh Now
            </Button>
          }
          sx={{
            backgroundColor: '#2196F3',
            '& .MuiAlert-message': {
              fontWeight: 500
            }
          }}
        >
          New version available! Refresh to get the latest features.
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Home;
