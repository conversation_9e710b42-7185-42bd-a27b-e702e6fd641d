import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Divider
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import RestoreIcon from '@mui/icons-material/Restore';
import ProfessionalButton from './common/ProfessionalButton';
import Chart from './Chart';
import { ChartData } from './ChartGrid';
import useAnalytics from '../hooks/useAnalytics';

// Utility function for smooth scroll to top
const scrollToTop = (smooth: boolean = true) => {
  const mainContentArea = document.querySelector('[data-main-content="true"]');
  if (mainContentArea) {
    mainContentArea.scrollTo({
      top: 0,
      left: 0,
      behavior: smooth ? 'smooth' : 'auto'
    });
  }
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: smooth ? 'smooth' : 'auto'
  });
};

interface ChartModifyPageProps {
  chartData: ChartData;
  onBack: () => void;
  onChartUpdate?: (updatedChart: ChartData) => void;
}



// Aggregation options
const AGGREGATION_OPTIONS = [
  { value: 'count', label: 'Count' },
  { value: 'sum', label: 'Sum' },
  { value: 'avg', label: 'Average' },
  { value: 'min', label: 'Minimum' },
  { value: 'max', label: 'Maximum' },
  { value: 'median', label: 'Median' }
];

// Multi-color palettes for charts that support multiple colors
const getMultiColorPalettes = () => {
  return [
    { name: 'Default', colors: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f'] },
    { name: 'Vibrant', colors: ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', '#e67e22', '#1abc9c', '#34495e'] },
    { name: 'Pastel', colors: ['#ffb3ba', '#bae1ff', '#baffc9', '#ffffba', '#ffdfba', '#c7ceea', '#ffd1dc', '#e6e6fa'] },
    { name: 'Professional', colors: ['#2c3e50', '#34495e', '#7f8c8d', '#95a5a6', '#bdc3c7', '#ecf0f1', '#3498db', '#e74c3c'] },
    { name: 'Ocean', colors: ['#006994', '#0085c3', '#00a6fb', '#7209b7', '#560bad', '#2d1b69', '#0f3460', '#2e8b57'] },
    { name: 'Warm', colors: ['#ff6b6b', '#ffa726', '#ffcc02', '#66bb6a', '#42a5f5', '#ab47bc', '#ef5350', '#ff7043'] }
  ];
};

// Gradient palettes specifically for heatmaps
const getHeatmapPalettes = () => {
  return [
    { name: 'Viridis', colors: ['#440154', '#31688e', '#35b779', '#fde725'] },
    { name: 'Plasma', colors: ['#0d0887', '#7e03a8', '#cc4778', '#f89441', '#f0f921'] },
    { name: 'Blues', colors: ['#f7fbff', '#c6dbef', '#6baed6', '#2171b5', '#08306b'] },
    { name: 'Reds', colors: ['#fff5f0', '#fcbba1', '#fb6a4a', '#cb181d', '#67000d'] },
    { name: 'Greens', colors: ['#f7fcf5', '#c7e9c0', '#74c476', '#238b45', '#00441b'] },
    { name: 'Oranges', colors: ['#fff5eb', '#fdd0a2', '#fd8d3c', '#d94801', '#7f2704'] },
    { name: 'Purples', colors: ['#fcfbfd', '#dadaeb', '#9e9ac8', '#6a51a3', '#3f007d'] }
  ];
};

// Helper function to get the correct Plotly chart type
const getPlotlyType = (chartType: string) => {
  const type = chartType.toLowerCase();

  // Handle stacked charts first
  if (type.includes('stacked') && type.includes('bar')) {
    return 'bar';
  }
  if (type.includes('stacked') && type.includes('line')) {
    return 'scatter';
  }

  // Map column chart to line chart (scatter with lines+markers)
  if (type === 'column') {
    return 'scatter';
  }
  if (type === 'line' || type.includes('multi_line') || type.includes('combo')) {
    return 'scatter';
  }
  if (type === 'horizontal_bar') {
    return 'bar';
  }
  if (type.includes('area')) {
    return 'scatter'; // Area charts use scatter with fill
  }

  return chartType;
};

// Determine chart color type
const getChartColorType = (chartType: string) => {
  const type = chartType.toLowerCase();

  // Single color charts - use color picker (added 'column' as single-color like line charts)
  if (['bar', 'horizontal_bar', 'line', 'column', 'scatter', 'histogram', 'area'].includes(type)) {
    return 'single';
  }

  // Stacked charts should use multi-color palettes (each series gets different color)
  if (type.includes('stacked')) {
    return 'multi';
  }

  // Multi-line and multi-area charts should use multi-color palettes
  if (type.includes('multi_') || type.includes('combo_')) {
    return 'multi';
  }

  // Heatmap charts - use heatmap palette dropdown
  if (['heatmap', 'contour'].includes(type)) {
    return 'heatmap';
  }

  // Multi-color charts - use multi-color palette dropdown
  if (['pie', 'donut', 'doughnut', 'funnel', 'polar_area', 'semi_circle', 'treemap', 'sunburst', 'icicle'].includes(type)) {
    return 'multi';
  }

  // Default to multi-color for unknown chart types
  return 'multi';
};

const ChartModifyPage: React.FC<ChartModifyPageProps> = ({
  chartData,
  onBack,
  onChartUpdate
}) => {
  const [modifiedChart, setModifiedChart] = useState<ChartData>(chartData);
  const [selectedPalette, setSelectedPalette] = useState(() => {
    const colorType = getChartColorType(chartData.chart_type);
    if (colorType === 'multi') {
      return getMultiColorPalettes()[0].name;
    } else if (colorType === 'heatmap') {
      return getHeatmapPalettes()[0].name;
    }
    return 'Default';
  });
  const [singleColor, setSingleColor] = useState('#1f77b4'); // Default blue for single color charts
  const [aggregationType, setAggregationType] = useState('count');
  const { trackNavigation } = useAnalytics();

  useEffect(() => {
    scrollToTop(false);
    trackNavigation('CHARTS_PAGE', 'CHART_MODIFY_PAGE', {
      chart_id: chartData.id,
      chart_type: chartData.chart_type
    });
  }, [chartData.id, chartData.chart_type, trackNavigation]);

  // Helper function to convert chart type to display label
  const getChartTypeLabel = (chartType: string): string => {
    const labelMap: { [key: string]: string } = {
      'pie': 'Pie Chart',
      'donut': 'Donut Chart',
      'doughnut': 'Doughnut Chart',
      'bar': 'Bar Chart',
      'column': 'Line Chart', // Map column to Line Chart in dropdown
      'horizontal_bar': 'Horizontal Bar Chart',
      'line': 'Line Chart',
      'scatter': 'Scatter Plot',
      'area': 'Area Chart',
      'heatmap': 'Heatmap',
      'histogram': 'Histogram',
      'box': 'Box Plot',
      'violin': 'Violin Plot',
      'treemap': 'Treemap',
      'sunburst': 'Sunburst',
      'funnel': 'Funnel Chart',
      'waterfall': 'Waterfall Chart',
      'polar_area': 'Polar Area Chart',
      'semi_circle': 'Semi Circle Chart',
      'multi_line': 'Multi Line Chart',
      'multi_area': 'Multi Area Chart',
      'combo_bar_line': 'Combo Bar Line Chart',
      'stacked_bar': 'Stacked Bar Chart',
      'stacked_line': 'Stacked Line Chart',
      'stacked_area': 'Stacked Area Chart'
    };
    return labelMap[chartType.toLowerCase()] || chartType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) + ' Chart';
  };

  // Get available chart types from backend API only
  const getAvailableChartTypes = () => {
    // Handle both string (comma-separated) and array formats for allowed_chart_types
    let chartTypes: string[] = [];

    if (chartData.allowed_chart_types) {
      if (typeof chartData.allowed_chart_types === 'string') {
        // Handle comma-separated string format
        chartTypes = chartData.allowed_chart_types
          .split(',')
          .map(type => type.trim())
          .filter(type => type.length > 0);
      } else if (Array.isArray(chartData.allowed_chart_types)) {
        // Handle array format
        chartTypes = chartData.allowed_chart_types.filter(type => type && type.length > 0);
      }
    }

    if (chartTypes.length > 0) {
      return chartTypes.map(chartType => ({
        value: chartType,
        label: getChartTypeLabel(chartType)
      }));
    }

    // If no allowed_chart_types, return empty array (should not happen in production)
    console.warn('No allowed_chart_types found in chart data');
    return [];
  };

  // Handle chart type change
  const handleChartTypeChange = (newType: string) => {
    // Transform data structure based on chart type
    const transformedData = transformDataForChartType(modifiedChart.data, modifiedChart.chart_type, newType);

    // Apply appropriate colors based on the new chart type
    const colorType = getChartColorType(newType);
    let finalData = transformedData;
    let newSelectedPalette = selectedPalette;
    let newSingleColor = singleColor;

    if (colorType === 'multi') {
      const multiPalettes = getMultiColorPalettes();
      newSelectedPalette = multiPalettes[0].name;
      setSelectedPalette(newSelectedPalette);
      const palette = multiPalettes[0];
      finalData = applyColorsToChartData(transformedData, newType, palette.colors);
    } else if (colorType === 'heatmap') {
      const heatmapPalettes = getHeatmapPalettes();
      newSelectedPalette = heatmapPalettes[0].name;
      setSelectedPalette(newSelectedPalette);
      const palette = heatmapPalettes[0];
      finalData = applyColorsToChartData(transformedData, newType, palette.colors);
    } else if (colorType === 'single') {
      newSingleColor = '#1f77b4'; // Reset to default blue
      setSingleColor(newSingleColor);
      finalData = applyColorsToChartData(transformedData, newType, [newSingleColor]);
    }

    // Update the chart type and data structure with colors applied
    const updatedChart = {
      ...modifiedChart,
      chart_type: newType,
      data: finalData
    };

    setModifiedChart(updatedChart);

    // Immediately notify parent component to persist the change
    if (onChartUpdate) {
      onChartUpdate(updatedChart);
    }
  };

  // Helper function to transform data structure when changing chart types
  const transformDataForChartType = (currentData: any, fromType: string, toType: string) => {
    if (!currentData) return currentData;

    const isCurrentArray = Array.isArray(currentData);
    const dataArray = isCurrentArray ? currentData : [currentData];

    // Special handling for charts that need multiple traces
    let workingDataArray = dataArray;

    // For combo charts - ensure we have at least 2 traces
    if (toType.includes('combo_bar_line') && dataArray.length === 1) {
      const originalTrace = dataArray[0];
      const secondTrace = {
        ...originalTrace,
        name: originalTrace.name ? `${originalTrace.name} Line` : 'Line Series',
        // Slightly modify the data for the second trace
        y: originalTrace.y ? originalTrace.y.map((val: number) => val * 0.8) : [16, 24, 20, 28, 32]
      };
      workingDataArray = [
        { ...originalTrace, name: originalTrace.name ? `${originalTrace.name} Bar` : 'Bar Series' },
        secondTrace
      ];
    }

    // For stacked bar charts - ensure we have at least 2 traces with same x values
    if (toType.includes('stacked') && toType.includes('bar') && dataArray.length === 1) {
      const originalTrace = dataArray[0];
      const secondTrace = {
        ...originalTrace,
        name: originalTrace.name ? `${originalTrace.name} Series 2` : 'Series 2',
        // Create different y values for the second series
        y: originalTrace.y ? originalTrace.y.map((val: number) => Math.round(val * 0.7)) : [14, 21, 17, 24, 28]
      };
      const thirdTrace = {
        ...originalTrace,
        name: originalTrace.name ? `${originalTrace.name} Series 3` : 'Series 3',
        // Create different y values for the third series
        y: originalTrace.y ? originalTrace.y.map((val: number) => Math.round(val * 0.5)) : [10, 15, 12, 17, 20]
      };
      workingDataArray = [
        { ...originalTrace, name: originalTrace.name ? `${originalTrace.name} Series 1` : 'Series 1' },
        secondTrace,
        thirdTrace
      ];
    }

    const transformedArray = workingDataArray.map(trace => {
      // Create a base trace with common properties
      const baseTrace = {
        ...trace,
        type: getPlotlyType(toType)
      };

      // Handle transformation from pie/donut/semi_circle to other chart types
      if (['pie', 'donut', 'doughnut', 'semi_circle'].includes(fromType) && !['pie', 'donut', 'doughnut', 'semi_circle'].includes(toType)) {
        return {
          ...baseTrace,
          x: trace.labels || trace.x || ['Category A', 'Category B', 'Category C', 'Category D', 'Category E'],
          y: trace.values || trace.y || [30, 25, 20, 15, 10],
          labels: undefined,
          values: undefined,
          hole: undefined,
          domain: undefined,
          rotation: undefined,
          direction: undefined,
          orientation: toType === 'horizontal_bar' ? 'h' : undefined,
          mode: (toType === 'line' || toType === 'column') ? 'lines+markers' : undefined
        };
      }

      // Handle transformation to pie/donut/semi_circle from other chart types
      if (!['pie', 'donut', 'doughnut', 'semi_circle'].includes(fromType) && ['pie', 'donut', 'doughnut', 'semi_circle'].includes(toType)) {
        return {
          ...baseTrace,
          labels: trace.x || trace.labels || ['Category A', 'Category B', 'Category C', 'Category D', 'Category E'],
          values: trace.y || trace.values || [30, 25, 20, 15, 10],
          x: undefined,
          y: undefined,
          orientation: undefined,
          hole: ['donut', 'doughnut'].includes(toType) ? 0.4 : (toType === 'semi_circle' ? 0.3 : undefined),
          rotation: toType === 'semi_circle' ? 90 : undefined,
          direction: toType === 'semi_circle' ? 'clockwise' : undefined,
          domain: toType === 'semi_circle' ? { x: [0, 1], y: [0, 0.5] } : undefined,
          mode: undefined
        };
      }

      // Handle stacked bar chart specific properties
      if (toType.includes('stacked') && toType.includes('bar')) {
        return {
          ...baseTrace,
          type: 'bar',
          mode: undefined,
          orientation: toType.includes('horizontal') ? 'h' : undefined,
          fill: undefined,
          stackgroup: undefined, // Bars stack automatically when they share x values
          // Ensure we have proper x and y data
          x: trace.x || trace.labels || ['Category A', 'Category B', 'Category C', 'Category D', 'Category E'],
          y: trace.y || trace.values || [20, 30, 25, 35, 40],
          labels: undefined,
          values: undefined
        };
      }

      // Handle stacked line chart specific properties
      if (toType.includes('stacked') && toType.includes('line')) {
        return {
          ...baseTrace,
          type: 'scatter',
          mode: 'lines+markers',
          orientation: undefined,
          fill: undefined, // No fill for stacked line charts
          stackgroup: 'one' // Enable stacking for line charts
        };
      }

      // Handle line chart specific properties (including column mapped to line)
      if (toType === 'line' || toType === 'column' || toType.includes('multi_line')) {
        return {
          ...baseTrace,
          mode: 'lines+markers',
          orientation: undefined,
          fill: undefined,
          stackgroup: undefined
        };
      }

      // Handle area chart specific properties
      if (toType.includes('area')) {
        return {
          ...baseTrace,
          mode: 'lines',
          fill: trace.fill || 'tozeroy',
          stackgroup: toType.includes('multi_') ? 'one' : undefined,
          orientation: undefined
        };
      }

      // Handle combo bar line chart - preserve mixed types
      if (toType.includes('combo_bar_line')) {
        // For combo charts, we need to ensure we have mixed trace types
        // Some traces should be bars, others should be lines
        const traceIndex = workingDataArray.indexOf(trace);
        const shouldBeBar = traceIndex % 2 === 0; // Alternate between bar and line

        return {
          ...baseTrace,
          type: shouldBeBar ? 'bar' : 'scatter',
          mode: shouldBeBar ? undefined : 'lines+markers',
          orientation: undefined,
          fill: undefined,
          stackgroup: undefined,
          // Ensure we have proper x and y data
          x: trace.x || trace.labels || ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
          y: trace.y || trace.values || [20, 30, 25, 35, 40],
          labels: undefined,
          values: undefined
        };
      }

      // Handle horizontal bar chart specific properties
      if (toType === 'horizontal_bar') {
        // For horizontal bars, ensure we have the correct data structure
        const originalX = trace.x || trace.labels || ['Category A', 'Category B', 'Category C', 'Category D', 'Category E'];
        const originalY = trace.y || trace.values || [30, 25, 20, 15, 10];

        return {
          ...baseTrace,
          type: 'bar',
          orientation: 'h',
          // Keep original x and y - the Chart component will handle the swapping for display
          x: originalX,
          y: originalY,
          mode: undefined,
          fill: undefined,
          stackgroup: undefined,
          labels: undefined,
          values: undefined
        };
      }

      // For other transformations, keep the current structure
      return baseTrace;
    });

    return isCurrentArray ? transformedArray : transformedArray[0];
  };

  // Handle color palette change for multi-color and heatmap charts
  const handlePaletteChange = (paletteName: string) => {
    setSelectedPalette(paletteName);
    const colorType = getChartColorType(modifiedChart.chart_type);

    let palette;
    if (colorType === 'multi') {
      palette = getMultiColorPalettes().find(p => p.name === paletteName);
    } else if (colorType === 'heatmap') {
      palette = getHeatmapPalettes().find(p => p.name === paletteName);
    }

    if (palette) {
      // Update chart data with new colors based on chart type
      const updatedChart = {
        ...modifiedChart,
        data: applyColorsToChartData(modifiedChart.data, modifiedChart.chart_type, palette.colors)
      };

      setModifiedChart(updatedChart);

      // Immediately notify parent component to persist the change
      if (onChartUpdate) {
        onChartUpdate(updatedChart);
      }
    }
  };

  // Handle single color change for single-color charts
  const handleSingleColorChange = (color: string) => {
    setSingleColor(color);

    // Update chart data with new single color
    const updatedChart = {
      ...modifiedChart,
      data: applyColorsToChartData(modifiedChart.data, modifiedChart.chart_type, [color])
    };

    setModifiedChart(updatedChart);

    // Immediately notify parent component to persist the change
    if (onChartUpdate) {
      onChartUpdate(updatedChart);
    }
  };

  // Helper function to apply colors based on chart type
  const applyColorsToChartData = (currentData: any, chartType: string, colors: string[]) => {
    if (!currentData) return currentData;

    const isArray = Array.isArray(currentData);
    const dataArray = isArray ? currentData : [currentData];
    const type = chartType.toLowerCase();

    const updatedArray = dataArray.map((trace, index) => {
      // Pie, donut, funnel, polar_area, semi_circle charts use marker.colors
      if (['pie', 'donut', 'doughnut', 'funnel', 'polar_area', 'semi_circle'].includes(type)) {
        return {
          ...trace,
          marker: {
            ...trace.marker,
            colors: colors // Use 'colors' array for pie slices
          }
        };
      }
      // Treemap, sunburst, icicle charts use marker.colors but different structure
      else if (['treemap', 'sunburst', 'icicle'].includes(type)) {
        return {
          ...trace,
          marker: {
            ...trace.marker,
            colors: colors, // Use 'colors' array for segments
            line: trace.marker?.line || { width: 1, color: 'white' } // Keep border lines
          }
        };
      }
      // Heatmaps use colorscale with proper format
      else if (['heatmap', 'contour'].includes(type)) {
        // Create colorscale array with proper format [[0, color1], [0.5, color2], [1, color3]]
        const colorscale = colors.map((color, i) => [
          i / Math.max(colors.length - 1, 1), // Normalize to 0-1 range
          color
        ]);

        return {
          ...trace,
          colorscale: colorscale,
          showscale: true, // Show color scale bar
          colorbar: {
            ...trace.colorbar,
            thickness: 15,
            len: 0.7
          }
        };
      }
      // Multi-line, multi-area, combo charts, and stacked charts use different colors per trace
      else if (type.includes('multi_') || type.includes('combo_') || type.includes('stacked')) {
        const traceColor = colors[index % colors.length];

        // For combo charts, handle bars and lines differently
        if (type.includes('combo_bar_line')) {
          const isBarTrace = trace.type === 'bar' || index % 2 === 0;

          if (isBarTrace) {
            return {
              ...trace,
              marker: {
                ...trace.marker,
                color: traceColor
              }
            };
          } else {
            return {
              ...trace,
              marker: {
                ...trace.marker,
                color: traceColor
              },
              line: {
                ...trace.line,
                color: traceColor
              }
            };
          }
        } else if (type.includes('stacked')) {
          // For stacked charts (bar, line, area)
          if (type.includes('bar')) {
            return {
              ...trace,
              marker: {
                ...trace.marker,
                color: traceColor
              }
            };
          } else {
            // For stacked line and area charts
            return {
              ...trace,
              marker: {
                ...trace.marker,
                color: traceColor
              },
              line: {
                ...trace.line,
                color: traceColor
              }
            };
          }
        } else {
          // For multi-line and multi-area charts
          return {
            ...trace,
            marker: {
              ...trace.marker,
              color: traceColor
            },
            line: {
              ...trace.line,
              color: traceColor
            }
          };
        }
      }
      // Bar, horizontal_bar, line, column, scatter charts use single color per trace
      else {
        return {
          ...trace,
          marker: {
            ...trace.marker,
            color: colors[index % colors.length]
          }
        };
      }
    });

    return isArray ? updatedArray : updatedArray[0];
  };



  // Handle aggregation change
  const handleAggregationChange = (newAgg: string) => {
    setAggregationType(newAgg);

    // Update the chart with new aggregation type
    const updatedChart = {
      ...modifiedChart,
      aggregation: newAgg
    };

    setModifiedChart(updatedChart);

    // Immediately notify parent component to persist the change
    if (onChartUpdate) {
      onChartUpdate(updatedChart);
    }
  };



  // Reset to original
  const handleReset = () => {
    // Reset to original chart data
    const resetChart = { ...chartData };

    setModifiedChart(resetChart);

    // Reset color selection based on chart type
    const colorType = getChartColorType(chartData.chart_type);
    if (colorType === 'multi') {
      const multiPalettes = getMultiColorPalettes();
      setSelectedPalette(multiPalettes[0].name);
    } else if (colorType === 'heatmap') {
      const heatmapPalettes = getHeatmapPalettes();
      setSelectedPalette(heatmapPalettes[0].name);
    } else if (colorType === 'single') {
      setSingleColor('#1f77b4'); // Reset to default blue
    }

    setAggregationType('count');

    // Notify parent component of the reset
    if (onChartUpdate) {
      onChartUpdate(resetChart);
    }
  };

  return (
    <Box sx={{
      width: '100%',
      minHeight: '100vh',
      backgroundColor: 'white',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <Box sx={{
        p: 3,
        borderBottom: '1px solid #e0e0e0',
        backgroundColor: 'white',
        position: 'sticky',
        top: 0,
        zIndex: 10
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* Left side - Back button */}
          <ProfessionalButton
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={onBack}
            size="small"
          >
            Back
          </ProfessionalButton>

          {/* Center - Page title */}
          <Typography variant="h5" sx={{
            fontWeight: 600,
            color: '#2c3e50',
            fontFamily: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
            textAlign: 'center',
            flex: 1,
            mx: 2,
          }}>
            Modify Chart
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            <ProfessionalButton
              variant="outlined"
              startIcon={<RestoreIcon />}
              onClick={handleReset}
              size="small"
            >
              Reset
            </ProfessionalButton>
          </Box>
        </Box>
      </Box>

      {/* Main Content */}
      <Box sx={{ 
        flexGrow: 1, 
        p: 3,
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', md: '1fr 400px' },
        gap: 3,
        alignItems: 'start'
      }}>
        {/* Chart Display Section */}
        <Paper
          elevation={0}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: 2,
            overflow: 'hidden',
            aspectRatio: '1.5/1',
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
          }}
        >
          <Chart
            data={modifiedChart}
            position="center"
            // Don't show modify icon in the chart itself - only pass other handlers
            onInsightsClick={undefined}
            onPreviewClick={undefined}
            onModifyClick={undefined}
            hideInsightsIcon={true}
          />
        </Paper>

        {/* Properties Panel */}
        <Paper
          elevation={0}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: 2,
            p: 3,
            boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
            height: 'fit-content'
          }}
        >
          <Typography variant="h6" sx={{ 
            mb: 3, 
            fontWeight: 600,
            color: '#2c3e50',
            fontFamily: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
          }}>
            Chart Properties
          </Typography>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Chart Type - Only show if allowed_chart_types is not empty */}
            {getAvailableChartTypes().length > 0 && (
              <FormControl fullWidth>
                <InputLabel>Chart Type</InputLabel>
                <Select
                  value={modifiedChart.chart_type}
                  label="Chart Type"
                  onChange={(e) => handleChartTypeChange(e.target.value)}
                >
                  {getAvailableChartTypes().map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}

            <Divider />

            {/* Color Selection - Dynamic based on chart type */}
            {(() => {
              const colorType = getChartColorType(modifiedChart.chart_type);

              if (colorType === 'single') {
                // Single color picker for bar, line, area, etc.
                return (
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                      Chart Color
                    </Typography>
                    <TextField
                      type="color"
                      value={singleColor}
                      onChange={(e) => handleSingleColorChange(e.target.value)}
                      fullWidth
                      size="small"
                      sx={{
                        '& .MuiInputBase-input': {
                          height: '40px',
                          cursor: 'pointer'
                        }
                      }}
                    />
                  </Box>
                );
              } else if (colorType === 'heatmap') {
                // Heatmap palette dropdown
                return (
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                      Heatmap Color Palette
                    </Typography>
                    <FormControl fullWidth>
                      <Select
                        value={selectedPalette}
                        onChange={(e) => handlePaletteChange(e.target.value)}
                        size="small"
                      >
                        {getHeatmapPalettes().map((palette) => (
                          <MenuItem key={palette.name} value={palette.name}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                              <Typography variant="body2" sx={{ minWidth: '80px' }}>
                                {palette.name}
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 0.25, flex: 1 }}>
                                {palette.colors.map((color, index) => (
                                  <Box
                                    key={index}
                                    sx={{
                                      width: 12,
                                      height: 12,
                                      backgroundColor: color,
                                      borderRadius: '2px',
                                      border: '1px solid rgba(0,0,0,0.1)',
                                      flex: 1
                                    }}
                                  />
                                ))}
                              </Box>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                );
              } else {
                // Multi-color palette dropdown for pie, treemap, etc.
                return (
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                      Color Palette
                    </Typography>
                    <FormControl fullWidth>
                      <Select
                        value={selectedPalette}
                        onChange={(e) => handlePaletteChange(e.target.value)}
                        size="small"
                      >
                        {getMultiColorPalettes().map((palette) => (
                          <MenuItem key={palette.name} value={palette.name}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                              <Typography variant="body2" sx={{ minWidth: '80px' }}>
                                {palette.name}
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 0.5 }}>
                                {palette.colors.map((color, index) => (
                                  <Box
                                    key={index}
                                    sx={{
                                      width: 16,
                                      height: 16,
                                      backgroundColor: color,
                                      borderRadius: '50%',
                                      border: '1px solid rgba(0,0,0,0.1)'
                                    }}
                                  />
                                ))}
                              </Box>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                );
              }
            })()}

            <Divider />

            {/* Aggregation Type */}
            <FormControl fullWidth>
              <InputLabel>Numerical Aggregation</InputLabel>
              <Select
                value={aggregationType}
                label="Numerical Aggregation"
                onChange={(e) => handleAggregationChange(e.target.value)}
              >
                {AGGREGATION_OPTIONS.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};

export default ChartModifyPage;
