import React, { useRef, useCallback } from 'react';
import { 
  Dialog, 
  DialogContent, 
  Box, 
  IconButton, 
  Typography 
} from '@mui/material';
import { toPng } from 'html-to-image';
import Plot from 'react-plotly.js';
import CloseIcon from '@mui/icons-material/Close';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import useAnalytics from '../../hooks/useAnalytics';

interface ChartPreviewModalProps {
  open: boolean;
  onClose: () => void;
  chartData: any;
}

const ChartPreviewModal: React.FC<ChartPreviewModalProps> = ({
  open,
  onClose,
  chartData
}) => {
  const plotContainerRef = useRef<HTMLDivElement>(null);
  const { trackChartEvent } = useAnalytics();

  // Helper function to get title text
  const getTitleText = (title: any): string => {
    if (!title) return '';
    if (typeof title === 'string') return title;
    if (typeof title === 'object' && title.text) return title.text;
    return '';
  };

  // Prepare chart data for display with numerical values (same logic as Chart component)
  const prepareData = () => {
    if (!chartData?.data) return [];

    const { data: rawData, chart_type } = chartData;
    let plotData = Array.isArray(rawData) ? rawData : [rawData];

    // Handle specific chart type transformations with numerical values
    const isDonutChart = ['donut', 'doughnut'].includes(chart_type.toLowerCase());
    if (chart_type === 'pie' || isDonutChart) {
      return plotData.map(trace => ({
        ...trace,
        type: 'pie',
        labels: trace.labels || trace.x,
        values: trace.values || trace.y,
        x: undefined,
        y: undefined,
        hole: isDonutChart ? 0.4 : 0,
        automargin: true,
        textinfo: 'percent+value', // Show only percentages and values, not labels (categories shown in legend)
        textposition: 'auto',
        textfont: {
          size: 12,
          color: '#212121',
          family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
        },
        hovertemplate: '<b>%{label}</b><br>Value: %{value}<br>Percent: %{percent}<extra></extra>'
      }));
    }

    if (chart_type === 'heatmap') {
      return plotData.map(trace => {
        // Helper function to determine if a color is light or dark
        const isLightColor = (colorscale: any) => {
          // For heatmap, we need to determine the overall brightness of the colorscale
          // Since heatmap cells will have varying colors, we'll use a smart approach
          // that works well with most colorscales by checking the middle color

          if (!colorscale || typeof colorscale === 'string') {
            // For string colorscales like 'Viridis', 'Plasma', etc., we know they're generally dark
            const lightColorscales = ['Blues', 'Reds', 'Greens', 'Oranges', 'Purples'];
            const colorscaleName = colorscale || 'Viridis';
            return lightColorscales.some(light => colorscaleName.includes(light));
          }

          if (Array.isArray(colorscale) && colorscale.length > 0) {
            // For custom colorscales, check the middle color
            const middleIndex = Math.floor(colorscale.length / 2);
            const middleColor = colorscale[middleIndex];
            const color = Array.isArray(middleColor) ? middleColor[1] : middleColor;

            // Convert hex to RGB and calculate luminance
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);

            // Calculate relative luminance using WCAG formula
            const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
            return luminance > 0.5; // Light if luminance > 0.5
          }

          return false; // Default to dark text on light background
        };

        const colorscale = trace.colorscale || 'Viridis';
        const isLight = isLightColor(colorscale);

        return {
          ...trace,
          type: 'heatmap',
          z: trace.z || trace.values,
          x: trace.x || trace.labels,
          y: trace.y,
          // Use colorscale from trace if available (set by ChartModifyPage), otherwise default to Viridis
          colorscale: colorscale,
          // Preserve other colorscale-related properties from trace
          showscale: trace.showscale !== undefined ? trace.showscale : true,
          colorbar: trace.colorbar || {
            thickness: 15,
            len: 0.7
          },
          text: trace.z || trace.values,
          texttemplate: '%{text}',
          textfont: {
            size: 12,
            color: isLight ? '#000000' : '#FFFFFF' // Black text on light colors, white text on dark colors
          },
          hovertemplate: '<b>%{x}</b><br><b>%{y}</b><br>Value: %{z}<extra></extra>',
        };
      });
    }

    // Handle bar charts with numeric value display
    if (chart_type === 'bar') {
      return plotData.map(trace => ({
        ...trace,
        type: 'bar',
        text: trace.y || trace.values,
        textposition: 'outside',
        textfont: {
          size: 12,
          color: '#212121',
          family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
        },
        hovertemplate: '<b>%{x}</b><br>Value: %{y}<extra></extra>',
        cliponaxis: false, // Prevent text from being clipped
      }));
    }

    // Handle horizontal bar charts
    if (chart_type === 'horizontal_bar') {
      return plotData.map(trace => {
        // For horizontal bars, we need to swap x and y data
        const originalX = trace.x || trace.labels || [];
        const originalY = trace.y || trace.values || [];

        return {
          ...trace,
          type: 'bar',
          orientation: 'h', // Horizontal orientation
          // Swap x and y for horizontal display
          x: originalY, // Values go on x-axis (horizontal)
          y: originalX, // Categories go on y-axis (vertical)
          text: originalY, // Use the values for text display
          textposition: 'outside',
          textfont: {
            size: 12,
            color: '#212121',
            family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
          },
          hovertemplate: '<b>%{y}</b><br>Value: %{x}<extra></extra>',
          cliponaxis: false, // Prevent text from being clipped
        };
      });
    }

    // Handle semi circle charts (half pie chart)
    if (chart_type === 'semi_circle') {
      return plotData.map(trace => {
        // Get original values and labels
        const values = trace.values || trace.y || [30, 25, 20, 15, 10];
        const labels = trace.labels || trace.x || ['Category A', 'Category B', 'Category C', 'Category D', 'Category E'];

        // Calculate total of actual data
        const totalValue = values.reduce((sum: number, val: number) => sum + val, 0);

        // Add a hidden slice equal to the total to create semi-circle effect
        const extendedValues = [...values, totalValue];
        const extendedLabels = [...labels, ''];

        // Create colors array with transparent color for hidden slice
        const originalColors = trace.marker?.colors || ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f'];
        const extendedColors = [...originalColors.slice(0, values.length), 'rgba(255,255,255,0)']; // Use white transparent instead of black

        return {
          ...trace,
          type: 'pie',
          labels: extendedLabels,
          values: extendedValues,
          x: undefined,
          y: undefined,
          // Semi-circle configuration - upward semi-circle
          rotation: 270, // Start from left (270 degrees) for proper upward semi-circle
          direction: 'clockwise',
          hole: 0, // No center hole - solid semi-circle
          automargin: true,
          textinfo: 'percent+value', // Show percentages and values
          textposition: 'auto',
          textfont: {
            size: 12,
            color: '#212121',
            family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
          },
          hovertemplate: '<b>%{label}</b><br>Value: %{value}<br>Percent: %{percent}<extra></extra>',
          // Apply colors including transparent for hidden slice
          marker: {
            colors: extendedColors,
            line: {
              color: '#FFFFFF',
              width: 2
            }
          },
          // Show legend only for actual data (not hidden slice)
          showlegend: true,
          // Custom hover info - hide hover for transparent slice
          hoverinfo: 'label+percent+value',
          // Ensure proper text display - hide text for transparent slice
          text: [...labels, ''], // Empty text for hidden slice
          texttemplate: '%{percent}<br>%{value}' // Custom text template
        };
      });
    }

    // Handle line charts with numeric value display (including column mapped to line)
    if (chart_type === 'line' || chart_type === 'column' || chart_type === 'scatter') {
      return plotData.map(trace => ({
        ...trace,
        type: 'scatter',
        mode: trace.mode || 'lines+markers+text',
        text: trace.y || trace.values,
        textposition: 'top center',
        textfont: {
          size: 11,
          color: '#212121',
          family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
        },
        hovertemplate: '<b>%{x}</b><br>Value: %{y}<extra></extra>',
        cliponaxis: false, // Prevent text from being clipped
      }));
    }

    // Handle area charts
    if (chart_type.toLowerCase().includes('area')) {
      return plotData.map((trace, index) => ({
        ...trace,
        type: 'scatter',
        mode: 'lines',
        fill: trace.fill || (index === 0 ? 'tozeroy' : 'tonexty'),
        line: {
          width: 0,
          ...trace.line
        },
        stackgroup: trace.stackgroup || 'one',
        hovertemplate: '<b>%{fullData.name}</b><br>%{x}<br>Value: %{y}<extra></extra>',
      }));
    }

    // Handle multi-metric charts
    if (chart_type.toLowerCase().includes('multi') || chart_type.toLowerCase().includes('multiple')) {
      return plotData.map((trace) => ({
        ...trace,
        type: 'scatter',
        mode: trace.mode || 'lines+markers',
        line: {
          width: 2.5,
          ...trace.line
        },
        marker: {
          size: 5,
          ...trace.marker
        },
        hovertemplate: '<b>%{fullData.name}</b><br>%{x}<br>Value: %{y}<extra></extra>',
      }));
    }

    // Default case: ensure type is set and add basic hover template
    return plotData.map(trace => ({
      ...trace,
      type: trace.type || chart_type,
      hovertemplate: trace.hovertemplate || '<b>%{x}</b><br>Value: %{y}<extra></extra>',
    }));
  };

  // Get chart layout with responsive sizing and axis titles
  const getLayout = () => {
    // Count legend items to determine positioning
    const legendCount = chartData?.data ? (Array.isArray(chartData.data) ? chartData.data.length : 1) : 0;
    const shouldUseSideLegend = legendCount > 4; // Use side legend for more than 4 items

    const baseLayout = {
      ...chartData?.layout,
      autosize: true,
      responsive: true,
      margin: shouldUseSideLegend ?
        { l: 100, r: 150, t: 80, b: 100 } : // More right margin for side legend
        { l: 100, r: 80, t: 100, b: 100 }, // More top margin for top legend
      font: {
        family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
        size: 14
      },
      paper_bgcolor: 'rgba(0,0,0,0)',
      plot_bgcolor: 'rgba(0,0,0,0)',
      showlegend: true,
      legend: shouldUseSideLegend ? {
        // Side legend for charts with many legend items
        orientation: 'v',
        x: 1.02,
        xanchor: 'left',
        y: 0.5,
        yanchor: 'middle',
        bgcolor: 'rgba(255,255,255,0.9)',
        bordercolor: 'rgba(0, 0, 0, 0.1)',
        borderwidth: 1,
        font: { size: 12, color: '#212121' }
      } : {
        // Top legend for charts with fewer legend items
        orientation: 'h',
        x: 0.5,
        xanchor: 'center',
        y: 1.02,
        yanchor: 'bottom',
        bgcolor: 'rgba(255,255,255,0.9)',
        bordercolor: 'rgba(0, 0, 0, 0.1)',
        borderwidth: 1,
        font: { size: 12, color: '#212121' }
      },
      // Include x-axis title if available
      xaxis: {
        ...chartData?.layout?.xaxis,
        title: {
          text: getTitleText(chartData?.layout?.xaxis?.title),
          font: {
            family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
            size: 14,
            color: '#2c3e50'
          },
          standoff: 20
        },
        tickfont: { size: 12, color: '#424242' },
        gridcolor: 'rgba(0, 0, 0, 0.1)',
        zerolinecolor: 'rgba(0, 0, 0, 0.2)',
        automargin: true
      },
      // Include y-axis title if available
      yaxis: {
        ...chartData?.layout?.yaxis,
        title: {
          text: getTitleText(chartData?.layout?.yaxis?.title),
          font: {
            family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
            size: 14,
            color: '#2c3e50'
          },
          standoff: 20
        },
        tickfont: { size: 12, color: '#424242' },
        gridcolor: 'rgba(0, 0, 0, 0.1)',
        zerolinecolor: 'rgba(0, 0, 0, 0.2)',
        automargin: true
      }
    };

    // Special handling for semi-circle charts
    if (chartData.chart_type === 'semi_circle') {
      baseLayout.margin = { l: 50, r: 100, t: 60, b: 30 }; // Optimized margins for preview modal

      // Add shape to clip the bottom half and create upward semi-circle effect
      baseLayout.shapes = [
        {
          type: 'rect',
          xref: 'paper',
          yref: 'paper',
          x0: 0,
          y0: 0,
          x1: 1,
          y1: 0.45, // Cover bottom half to show only upward semi-circle (reduced from 0.5 to 0.45)
          fillcolor: 'rgba(255,255,255,1)', // Solid white background
          line: {
            width: 0
          },
          layer: 'above'
        }
      ];
    }

    return baseLayout;
  };

  // Copy chart to clipboard
  const handleCopyChart = useCallback(async (event: React.MouseEvent<HTMLElement>) => {
    if (!plotContainerRef.current) {
      console.warn('Chart not ready for copying');
      return;
    }

    const copyButton = event.currentTarget;
    let originalContent = '';
    let originalTitle = '';
    
    if (copyButton) {
      originalContent = copyButton.innerHTML;
      originalTitle = copyButton.getAttribute('title') || '';
      copyButton.innerHTML = '⏳';
      copyButton.setAttribute('title', 'Copying...');
    }

    try {
      // Hide action buttons temporarily
      const actionButtons = plotContainerRef.current.querySelector('.preview-action-buttons');
      let originalDisplay = 'flex';
      if (actionButtons instanceof HTMLElement) {
        originalDisplay = actionButtons.style.display || 'flex';
        actionButtons.style.display = 'none';
      }

      // Create PNG
      const dataUrl = await toPng(plotContainerRef.current, {
        backgroundColor: 'transparent',
        quality: 1.0,
        pixelRatio: 2,
      });

      // Restore action buttons display
      if (actionButtons instanceof HTMLElement) {
        actionButtons.style.display = originalDisplay;
      }

      // Create a temporary image element
      const img = document.createElement('img');
      img.src = dataUrl;

      img.onload = async () => {
        try {
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;

          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.drawImage(img, 0, 0);

            canvas.toBlob(async (blob) => {
              if (blob) {
                try {
                  const item = new ClipboardItem({ 'image/png': blob });
                  await navigator.clipboard.write([item]);

                  // Track successful copy
                  await trackChartEvent('CHART_COPY', chartData.chart_type, chartData.id, {
                    success: true,
                    format: 'png',
                    source: 'preview_modal',
                    chart_title: getTitleText(chartData.layout?.title) || 'Chart'
                  });

                  // Show success feedback
                  if (copyButton) {
                    copyButton.innerHTML = '✅';
                    copyButton.setAttribute('title', 'Copied successfully!');
                    setTimeout(() => {
                      copyButton.innerHTML = originalContent;
                      copyButton.setAttribute('title', originalTitle);
                    }, 2000);
                  }
                  
                  console.log('✅ Chart copied to clipboard successfully!');
                } catch (clipboardError) {
                  console.error('Error copying to clipboard:', clipboardError);
                  
                  if (copyButton) {
                    copyButton.innerHTML = '❌';
                    copyButton.setAttribute('title', 'Copy failed - try again');
                    setTimeout(() => {
                      copyButton.innerHTML = originalContent;
                      copyButton.setAttribute('title', originalTitle);
                    }, 2000);
                  }
                }
              }
            }, 'image/png');
          }
        } catch (canvasError) {
          console.error('Error creating canvas:', canvasError);
          
          if (copyButton) {
            copyButton.innerHTML = '❌';
            copyButton.setAttribute('title', 'Processing failed');
            setTimeout(() => {
              copyButton.innerHTML = originalContent;
              copyButton.setAttribute('title', originalTitle);
            }, 2000);
          }
        }
      };

      img.onerror = () => {
        if (copyButton) {
          copyButton.innerHTML = '❌';
          copyButton.setAttribute('title', 'Image load failed');
          setTimeout(() => {
            copyButton.innerHTML = originalContent;
            copyButton.setAttribute('title', originalTitle);
          }, 2000);
        }
      };
    } catch (error) {
      console.error('Error copying chart to clipboard:', error);
      
      if (copyButton) {
        copyButton.innerHTML = '❌';
        copyButton.setAttribute('title', 'Copy failed');
        setTimeout(() => {
          copyButton.innerHTML = originalContent;
          copyButton.setAttribute('title', originalTitle);
        }, 2000);
      }
    }
  }, [chartData, trackChartEvent]);

  // Download chart as PNG
  const handleDownloadChart = useCallback(async () => {
    if (!plotContainerRef.current) return;

    try {
      const chartTitle = getTitleText(chartData?.layout?.title) || 'chart';
      const filename = `${chartTitle.toLowerCase().replace(/\s+/g, '_')}_preview.png`;

      // Hide action buttons temporarily
      const actionButtons = plotContainerRef.current.querySelector('.preview-action-buttons');
      let originalDisplay = 'flex';
      if (actionButtons instanceof HTMLElement) {
        originalDisplay = actionButtons.style.display || 'flex';
        actionButtons.style.display = 'none';
      }

      // Create PNG
      const dataUrl = await toPng(plotContainerRef.current, {
        backgroundColor: 'transparent',
        quality: 1.0,
        pixelRatio: 2,
      });

      // Restore action buttons display
      if (actionButtons instanceof HTMLElement) {
        actionButtons.style.display = originalDisplay;
      }

      // Create download link
      const link = document.createElement('a');
      link.download = filename;
      link.href = dataUrl;
      link.click();

      // Track successful download
      await trackChartEvent('CHART_DOWNLOAD', chartData.chart_type, chartData.id, {
        success: true,
        format: 'png',
        source: 'preview_modal',
        filename: filename,
        chart_title: chartTitle || 'Chart'
      });

      console.log('✅ Chart downloaded successfully!');
    } catch (error) {
      console.error('❌ Error saving chart as PNG:', error);

      await trackChartEvent('ERROR_OCCURRED', chartData.chart_type, chartData.id, {
        error_type: 'chart_download_failed',
        error_message: error instanceof Error ? error.message : 'Unknown error',
        source: 'preview_modal',
        format: 'png'
      });
    }
  }, [chartData, trackChartEvent]);

  if (!chartData) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          borderRadius: 3,
          maxHeight: '95vh',
          height: '95vh',
          margin: 1,
          overflow: 'hidden', // Prevent scrollbars
        }
      }}
    >
      <DialogContent sx={{ p: 0, position: 'relative', height: '100%', overflow: 'hidden' }}>
        {/* Close Button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 10,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 1)',
            }
          }}
        >
          <CloseIcon />
        </IconButton>

        {/* Chart Container */}
        <Box
          ref={plotContainerRef}
          sx={{
            width: '100%',
            height: '100%',
            position: 'relative',
            p: 2,
            pt: 4, // Reduced padding top for close button
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          }}
        >
          {/* Chart Title - Only show if title exists */}
          {getTitleText(chartData?.layout?.title) && (
            <Typography
              variant="h5"
              sx={{
                textAlign: 'center',
                mb: 1,
                fontWeight: 600,
                color: '#2c3e50',
                fontFamily: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
                flexShrink: 0,
              }}
            >
              {getTitleText(chartData?.layout?.title)}
            </Typography>
          )}

          {/* Action Buttons */}
          <Box
            className="preview-action-buttons"
            sx={{
              position: 'absolute',
              bottom: 20,
              left: 20,
              zIndex: 5,
              display: 'flex',
              gap: 1
            }}
          >
            {/* Copy Button */}
            <Box
              component="button"
              onClick={handleCopyChart}
              title="Copy to Clipboard"
              sx={{
                cursor: 'pointer',
                background: 'rgba(255, 255, 255, 0.9)',
                border: '1px solid rgba(0, 0, 0, 0.08)',
                padding: '8px',
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  boxShadow: '0 4px 8px rgba(0, 0, 0, 0.15)'
                }
              }}
            >
              <ContentCopyIcon sx={{ fontSize: '1.4rem', color: '#4caf50' }} />
            </Box>

            {/* Download Button */}
            <Box
              component="button"
              onClick={handleDownloadChart}
              title="Download as PNG"
              sx={{
                cursor: 'pointer',
                background: 'rgba(255, 255, 255, 0.9)',
                border: '1px solid rgba(0, 0, 0, 0.08)',
                padding: '8px',
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  boxShadow: '0 4px 8px rgba(0, 0, 0, 0.15)'
                }
              }}
            >
              <DownloadIcon sx={{ fontSize: '1.4rem', color: '#ff9800' }} />
            </Box>
          </Box>

          {/* Plotly Chart */}
          <Box sx={{
            width: '100%',
            flex: 1,
            minHeight: 0, // Important for flex child to shrink
            overflow: 'hidden'
          }}>
            <Plot
              data={prepareData()}
              layout={getLayout()}
              config={{
                displayModeBar: false,
                responsive: true,
                doubleClick: 'reset',
                showTips: false,
              }}
              style={{ width: '100%', height: '100%' }}
              useResizeHandler={true}
            />
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default ChartPreviewModal;
