// src/components/common/VersionChecker.tsx
import React, { useEffect, useState } from 'react';
import { Snackbar, Alert, Button, IconButton } from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import CloseIcon from '@mui/icons-material/Close';
import { useVersionCheck } from '../../hooks/useVersionCheck';

interface VersionCheckerProps {
  checkInterval?: number; // in milliseconds
  showNotification?: boolean;
  autoRefresh?: boolean;
}

const VersionChecker: React.FC<VersionCheckerProps> = ({
  checkInterval = 5 * 60 * 1000, // 5 minutes default
  showNotification = true,
  autoRefresh = false
}) => {
  const { hasUpdate, refreshApp } = useVersionCheck({
    checkInterval,
    forceRefreshOnUpdate: autoRefresh,
    showUpdateNotification: false, // We'll handle our own notification
    enableAutoRefresh: true
  });

  const [showUpdateSnackbar, setShowUpdateSnackbar] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  // Show notification when update is available
  useEffect(() => {
    if (hasUpdate && showNotification && !dismissed) {
      setShowUpdateSnackbar(true);
    }
  }, [hasUpdate, showNotification, dismissed]);

  // Handle refresh
  const handleRefresh = () => {
    setShowUpdateSnackbar(false);
    refreshApp();
  };

  // Handle dismiss
  const handleDismiss = () => {
    setShowUpdateSnackbar(false);
    setDismissed(true);
    
    // Reset dismissed state after 10 minutes
    setTimeout(() => {
      setDismissed(false);
    }, 10 * 60 * 1000);
  };

  if (!showNotification) {
    return null;
  }

  return (
    <Snackbar
      open={showUpdateSnackbar}
      onClose={handleDismiss}
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      sx={{ 
        mt: 8, // Account for fixed header
        zIndex: 9999 // Ensure it's above other elements
      }}
      autoHideDuration={null} // Don't auto-hide
    >
      <Alert
        severity="info"
        variant="filled"
        action={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Button
              color="inherit"
              size="small"
              onClick={handleRefresh}
              startIcon={<RefreshIcon />}
              sx={{ 
                fontWeight: 600,
                minWidth: 'auto',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)'
                }
              }}
            >
              Refresh
            </Button>
            <IconButton
              size="small"
              color="inherit"
              onClick={handleDismiss}
              sx={{
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)'
                }
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </div>
        }
        sx={{
          backgroundColor: '#2196F3',
          color: 'white',
          '& .MuiAlert-message': {
            fontWeight: 500,
            fontSize: '0.875rem'
          },
          '& .MuiAlert-action': {
            padding: 0,
            marginRight: 0
          }
        }}
      >
        🚀 New version available! Refresh to get the latest features and improvements.
      </Alert>
    </Snackbar>
  );
};

export default VersionChecker;
