// src/hooks/useVersionCheck.ts
import { useEffect, useRef, useState } from 'react';

interface VersionCheckConfig {
  checkInterval?: number; // in milliseconds, default 5 minutes
  forceRefreshOnUpdate?: boolean; // default true
  showUpdateNotification?: boolean; // default true
  enableAutoRefresh?: boolean; // default true
}

interface VersionInfo {
  version: string;
  buildTime: string;
  hash: string;
}

const DEFAULT_CONFIG: Required<VersionCheckConfig> = {
  checkInterval: 5 * 60 * 1000, // 5 minutes
  forceRefreshOnUpdate: true,
  showUpdateNotification: true,
  enableAutoRefresh: true
};

// Get current version info from build
const getCurrentVersion = (): VersionInfo => {
  // In production, these would be injected during build
  return {
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    buildTime: import.meta.env.VITE_BUILD_TIME || new Date().toISOString(),
    hash: import.meta.env.VITE_BUILD_HASH || Math.random().toString(36).substring(7)
  };
};

// Store version in localStorage for comparison
const VERSION_STORAGE_KEY = 'app_version_info';
const LAST_CHECK_KEY = 'version_last_check';

export const useVersionCheck = (config: VersionCheckConfig = {}) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const [hasUpdate, setHasUpdate] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [currentVersion, setCurrentVersion] = useState<VersionInfo | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const notificationShownRef = useRef(false);

  // Clear browser cache
  const clearCache = async () => {
    try {
      // Clear service worker cache if available
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        for (const registration of registrations) {
          await registration.unregister();
        }
      }

      // Clear browser cache using Cache API
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }

      // Clear localStorage version info
      localStorage.removeItem(VERSION_STORAGE_KEY);
      localStorage.removeItem(LAST_CHECK_KEY);
      
      console.log('✅ Cache cleared successfully');
    } catch (error) {
      console.warn('⚠️ Could not clear all caches:', error);
    }
  };

  // Force refresh the page
  const forceRefresh = async () => {
    await clearCache();
    
    // Use location.reload with force parameter
    if (typeof window !== 'undefined') {
      // Add timestamp to force bypass cache
      const url = new URL(window.location.href);
      url.searchParams.set('_t', Date.now().toString());
      window.location.href = url.toString();
    }
  };

  // Check for version updates
  const checkForUpdates = async () => {
    if (isChecking) return;
    
    setIsChecking(true);
    
    try {
      const current = getCurrentVersion();
      setCurrentVersion(current);
      
      // Get stored version
      const storedVersionStr = localStorage.getItem(VERSION_STORAGE_KEY);
      const storedVersion: VersionInfo | null = storedVersionStr ? JSON.parse(storedVersionStr) : null;
      
      // If no stored version, store current and continue
      if (!storedVersion) {
        localStorage.setItem(VERSION_STORAGE_KEY, JSON.stringify(current));
        localStorage.setItem(LAST_CHECK_KEY, Date.now().toString());
        setIsChecking(false);
        return;
      }
      
      // Check if version has changed
      const hasVersionChanged = 
        storedVersion.version !== current.version ||
        storedVersion.buildTime !== current.buildTime ||
        storedVersion.hash !== current.hash;
      
      if (hasVersionChanged) {
        console.log('🔄 New version detected:', {
          old: storedVersion,
          new: current
        });
        
        setHasUpdate(true);
        
        // Store new version
        localStorage.setItem(VERSION_STORAGE_KEY, JSON.stringify(current));
        
        // Show notification if enabled
        if (finalConfig.showUpdateNotification && !notificationShownRef.current) {
          notificationShownRef.current = true;
          
          // Create a subtle notification
          const notification = document.createElement('div');
          notification.innerHTML = `
            <div style="
              position: fixed;
              top: 20px;
              right: 20px;
              background: #4CAF50;
              color: white;
              padding: 12px 20px;
              border-radius: 8px;
              box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              z-index: 10000;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              font-size: 14px;
              max-width: 300px;
              cursor: pointer;
              transition: all 0.3s ease;
            ">
              <div style="font-weight: 600; margin-bottom: 4px;">
                🚀 New Version Available!
              </div>
              <div style="font-size: 12px; opacity: 0.9;">
                Click to refresh and get the latest features
              </div>
            </div>
          `;
          
          notification.onclick = () => {
            document.body.removeChild(notification);
            forceRefresh();
          };
          
          document.body.appendChild(notification);
          
          // Auto-remove notification after 10 seconds
          setTimeout(() => {
            if (document.body.contains(notification)) {
              document.body.removeChild(notification);
            }
          }, 10000);
        }
        
        // Auto-refresh if enabled
        if (finalConfig.forceRefreshOnUpdate && finalConfig.enableAutoRefresh) {
          // Wait a bit to show notification first
          setTimeout(() => {
            forceRefresh();
          }, 2000);
        }
      }
      
      localStorage.setItem(LAST_CHECK_KEY, Date.now().toString());
      
    } catch (error) {
      console.error('❌ Error checking for updates:', error);
    } finally {
      setIsChecking(false);
    }
  };

  // Manual refresh function
  const refreshApp = () => {
    forceRefresh();
  };

  // Initialize version checking
  useEffect(() => {
    // Initial check
    checkForUpdates();
    
    // Set up periodic checking
    if (finalConfig.enableAutoRefresh) {
      intervalRef.current = setInterval(checkForUpdates, finalConfig.checkInterval);
    }
    
    // Check on window focus (user returns to tab)
    const handleFocus = () => {
      const lastCheck = localStorage.getItem(LAST_CHECK_KEY);
      const timeSinceLastCheck = lastCheck ? Date.now() - parseInt(lastCheck) : Infinity;
      
      // Check if it's been more than 1 minute since last check
      if (timeSinceLastCheck > 60000) {
        checkForUpdates();
      }
    };
    
    window.addEventListener('focus', handleFocus);
    
    // Cleanup
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      window.removeEventListener('focus', handleFocus);
    };
  }, [finalConfig.checkInterval, finalConfig.enableAutoRefresh]);

  return {
    hasUpdate,
    isChecking,
    currentVersion,
    checkForUpdates,
    refreshApp,
    clearCache
  };
};

// Utility function to force clear cache and refresh (can be called from anywhere)
export const forceAppRefresh = async () => {
  try {
    // Clear service worker cache
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      for (const registration of registrations) {
        await registration.unregister();
      }
    }

    // Clear browser cache
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
    }

    // Clear localStorage
    localStorage.removeItem(VERSION_STORAGE_KEY);
    localStorage.removeItem(LAST_CHECK_KEY);
    
    // Force reload with cache bypass
    window.location.reload();
  } catch (error) {
    console.error('Error forcing app refresh:', error);
    // Fallback to simple reload
    window.location.reload();
  }
};
